@import '../../configurations/theme-colors';

.farm-alert-box {
	border-radius: 5px;
	display: flex;
	padding: 8px 16px;
	position: relative;
	min-height: 40px;

	@each $color in $theme-colors-list {
		&#{'[color=' + $color + ']'} {
			background-color: themeColor($color, 'lighten');
			color: themeColor($color, 'darken');
		}
	}

	&__content {
		flex: 1;

		&--with-dismissable {
			margin-right: 40px;
		}
	}

	&__icon.farm-icon {
		align-items: self-start;
		display: flex;
		margin-right: 16px;
		margin-top: 3px;
	}

	&__close {
		position: absolute;
		right: 8px;
		bottom: 50%;
		transform: translateY(50%);
	}

	&--dense {
		display: inline-flex;
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
	opacity: 0;
}
