name: publish Vue Package

on:
  push:
    branches:
      - main

env: 
  NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout source code
        uses: actions/checkout@master
      
      - uses: actions/setup-node@v3
        with:
          node-version: 14.19
          registry-url: https://registry.npmjs.org/

      - name: Cache node modules
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
            
      - name: Install
        run: npm install
        
      - name: Build
        run: npm run build 

      - name: Publish to NPM
        run: npm publish --access public 
        env: 
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

