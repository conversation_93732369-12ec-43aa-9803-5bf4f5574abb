@import '../../configurations/theme-colors';

.farm-tabs {
	width: 100%;
}
.tabs {
	display: flex;
	margin-bottom: -1px;

	&__tab {
		border-bottom: 1px solid var(--farm-primary-lighten);
		cursor: pointer;
		display: inline-flex;
		padding: 18px 24px 14px;
		height: 54px;
		transition: all 0.5s ease;
		&:hover {
			background-color: transparentize(themeColor('primary', 'base'), 0.86);

			div.rounded-circle {
				opacity: 0.86;
			}
		}

		span {
			font-weight: 400;
		}

		&--selected {

			border-bottom: 4px solid var(--farm-primary-base);

			span {
				font-size: 14px;
				font-weight: 500;
			}
		}
	}

	&--disabled {
		.tabs__tab {
			cursor: default;

			&:hover {
				background-color: transparent;
			}

			&--selected {
				&:hover {
					background-color: transparent;
				}
			}
		}
	}
}

.farm-tabs {
	border-bottom: 1px solid var(--farm-primary-lighten);
}


div.rounded-circle {
	background-color: var(--farm-gray-lighten);
	width: 24px;
	height: 24px;
	margin-top: -2px;
	margin-left: -2px;

	&.is-selected {
		background-color: var(--farm-primary-base);
	}
}