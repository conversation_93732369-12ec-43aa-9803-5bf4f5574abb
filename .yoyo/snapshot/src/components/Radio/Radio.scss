@import '../../configurations/variables';
@import '../../configurations/theme-colors';

.farm-radio-wrapper {
	align-items: center;
	display: flex;
}

input[type='radio'] {
	-webkit-appearance: none;
	appearance: none;
	background-color: #ffffff;
	margin: 0;
	font: inherit;
	color: rgba(0, 0, 0, 0.6);
	border: 2.5px solid themeColor('neutral', 'darken');
	border-radius: 50%;
	display: grid;
	place-content: center;
	cursor: pointer;

	&::before {
		content: '';
		border-radius: 50%;
		transform: scale(0);
		transition: 120ms transform ease-in-out;
	}

	&.farm-radio--checked::before {
		transform: scale(1);
	}

	&:focus {
		outline: none;
		outline-offset: none;
	}

	&:hover {
		box-shadow: 0px 0px 0px 8px rgba(0, 0, 0, 0.1);
		background-color: rgba(0, 0, 0, 0.1);
		border-radius: 50%;
		opacity: 1;
	}

	&:active {
		animation: pulse 0.2s 1 ease-out;
	}

	@each $color in $theme-colors-list {
		&#{'[color=' + $color + ']'} {
			&.farm-radio--checked::before {
				box-shadow: inset 16px 16px themeColor($color, 'base');
			}

			&.farm-radio--checked {
				border-color: themeColor($color, 'base');
			}
		}
	}

	&:disabled {
		border: 2.5px solid themeColor('neutral', 'base');
		cursor: auto;

		&.farm-radio--checked::before {
			box-shadow: inset 16px 16px themeColor('neutral', 'base');
		}

		&.farm-radio--checked {
			border-color: themeColor('neutral', 'base');
		}

		&:hover,
		&.farm-radio--checked:hover {
			box-shadow: none;
			background-color: transparent;
			border-radius: 50%;
			opacity: 1;
		}
	}
}

@each $size,
$value in $sizes {
	#{'input[type="radio"][size=' + $size + ']'} {
		width: $value;
		height: $value;

		&::before {
			content: '';

			@if $value == 14px {
				width: 6px;
				height: 6px;
			}

			@else {
				width: $value / 2;
				height: $value / 2;
			}
		}
	}
}

.farm-label {
	margin-left: 10px;
	margin-bottom: 0;
}