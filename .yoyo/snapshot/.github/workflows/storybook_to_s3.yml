name: publish docs

on:
  push:
    branches:
      - docs

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: "us-east-1"

    steps:
      - name: Checkout source code
        uses: actions/checkout@master

      - uses: actions/setup-node@v3
        with:
          node-version: 14.19
          registry-url: https://registry.npmjs.org/

      - name: Install
        run: npm install

      - name: Get package version
        run: node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV
      
      - name: Write the package version in introduction file
        run: sed -i 's/{VERSION}/${{ env.PACKAGE_VERSION }}/' src/stories/Introduction.stories.mdx
      
      - name: Build storybook
        run: npm run build-storybook
      - name: Clear bucket
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: aws s3 rm s3://front-farm-storybook --recursive --region us-east-1

      - name: Copy storybook
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          aws s3 cp \
            --recursive \
            --region us-east-1 \
            ./storybook-static s3://front-farm-storybook/

      - name: Invalidate-cf
        uses: chetan/invalidate-cloudfront-action@master
        env:
          DISTRIBUTION: E14B42WCS0GWH
          PATHS: "/*"
