@import '../../../configurations/mixins.scss';
@import '../../../configurations/functions.scss';

.farm-col {
    padding: 0 gutter('vuetify');
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
    width: 100%;

    &--no-gutters {
        padding: 0;
    }

    @each $k in $aligns {
        &#{'--align-' + $k} {
            align-self: $k !important;
        }
    }
}

@for $i from 1 through 12 {
    .farm-col--cols-#{$i * 1} {
        flex: 0 0 (100/12 * $i)+#{"%"};
        max-width: (100/12 * $i)+#{"%"};
    }

    .farm-col--xs-#{$i * 1} {
        flex: 0 0 (100/12 * $i)+#{"%"};
        max-width: (100/12 * $i)+#{"%"};
    }

    .farm-col--offset-#{$i * 1} {
        margin-left: (100/12 * $i)+#{"%"};
    }
}

.farm-col--offset-0 {
    margin-left: 0%;
}

@include fromXs {
    @for $i from 1 through 12 {
        .farm-col--sm-#{$i * 1} {
            flex: 0 0 (100/12 * $i)+#{"%"};
            max-width: (100/12 * $i)+#{"%"};
        }

        .farm-col--offset-sm-#{$i * 1} {
            margin-left: (100/12 * $i)+#{"%"};
        }
    }

    .farm-col--offset-sm-0 {
        margin-left: 0%;
    }
}

@include fromSm {
    @for $i from 1 through 12 {
        .farm-col--md-#{$i * 1} {
            flex: 0 0 (100/12 * $i)+#{"%"};
            max-width: (100/12 * $i)+#{"%"};
        }

        .farm-col--offset-md-#{$i * 1} {
            margin-left: (100/12 * $i)+#{"%"};
        }

    }

    .farm-col--offset-md-0 {
        margin-left: 0%;
    }

}

@include fromMd {
    @for $i from 1 through 12 {
        .farm-col--lg-#{$i * 1} {
            flex: 0 0 (100/12 * $i)+#{"%"};
            max-width: (100/12 * $i)+#{"%"};
        }

        .farm-col--offset-lg-#{$i * 1} {
            margin-left: (100/12 * $i)+#{"%"};
        }
    }

    .farm-col--offset-lg-0 {
        margin-left: 0%;
    }
}

@include fromLg {
    @for $i from 1 through 12 {
        .farm-col--xl-#{$i * 1} {
            flex: 0 0 (100/12 * $i)+#{"%"};
            max-width: (100/12 * $i)+#{"%"};
        }

        .farm-col--offset-xl-#{$i * 1} {
            margin-left: (100/12 * $i)+#{"%"};
        }
    }

    .farm-col--offset-xl-0 {
        margin-left: 0%;
    }
}