@mixin loggerMessage($color) {
	> .farm-icon {
		border-color: $color;
		background-color: $color;
	}

	.farm-bodytext--2 {
		color: $color;
	}
}

.logger__item {
	display: flex;
	flex-direction: row;
	height: 74px;
	align-items: center;

	> div {
		display: flex;
		flex-direction: column;
		padding-left: 8px;

		.logger__item--spacing-bottom {
			margin-bottom: 8px;
		}
	}

	> .farm-icon {
		border: 1px solid var(--farm-stroke-base);
		border-radius: 50%;
		min-width: 32px;
		min-height: 32px;
		width: 32px;
		height: 32px;
		display: block;

		display: flex;
		align-items: center;
		justify-content: center;

		border-color: var(--farm-neutral-base);
		background-color: var(--farm-neutral-base);

		&:before {
			color: white;
		}
	}

	> .farm-btn.logger__item--button {
		margin-left: 8px;
	}

	&.logger__item--error {
		@include loggerMessage(var(--farm-error-base));
	}

	&.logger__item--success {
		@include loggerMessage(var(--farm-success-base));
	}

	&.logger__item--info {
		@include loggerMessage(var(--farm-info-base));
	}

	&.logger__item--warning {
		@include loggerMessage(var(--farm-warning-base));
	}

	&--horizontal {
		flex-direction: column;
		height: auto;
		max-width: 230px;

		> div {
			align-items: center;
			text-align: center;
			gap: 8px;
			margin-bottom: 8px;
			padding-left: 0;

			.logger__item--spacing-bottom {
				margin-bottom: 0;
			}
		}

		> .farm-icon {
			margin-bottom: 8px;
		}

		> .farm-btn.logger__item--button {
			margin-left: 0;
		}
	}

	&--left-aligned {
		align-items: flex-start;

		> div {
			align-items: flex-start;
			text-align: left;
		}
	}
}
