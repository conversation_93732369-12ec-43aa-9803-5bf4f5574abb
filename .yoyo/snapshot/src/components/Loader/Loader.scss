$loader-rotate-animation: loader-rotate 1.4s linear infinite !default;
$loader-rotate-dash: loader-dash 1.4s ease-in-out infinite !default;
$loader-intermediate-svg-transition: all 0.2s ease-in-out !default;
$loader-overlay-transition: all 0.6s ease-in-out !default;

.farm-loader {
	width: 70px;
	height: 70px;
	color: var(--farm-primary-base) !important;
	caret-color: var(--farm-primary-base) !important;
	position: relative;
	display: inline-flex;
	vertical-align: middle;
	justify-content: center;
	align-items: center;

	& circle {
		stroke-width: 3.7;
	}

	&--small {
		width: 35px;
		height: 35px;

		& circle {
			stroke-width: 5.7;
		}
	}

	&--big {
		width: 100px;
		height: 100px;

		& circle {
			stroke-width: 2.7;
		}
	}

	> svg {
		width: 100%;
		height: 100%;
		margin: auto;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 0;
	}

	&--visible {
		animation: loader-dash 1.4s ease-in-out infinite;
		stroke-linecap: round;
		stroke-dasharray: 80, 200;
		stroke-dashoffset: 0px;
	}

	&--indeterminate {
		> svg {
			animation: $loader-rotate-animation;
			transform-origin: center center;
			transition: $loader-intermediate-svg-transition;
		}

		.farm-loader__overlay {
			animation: $loader-rotate-dash;
			stroke-linecap: round;
			stroke-dasharray: 80, 200;
			stroke-dashoffset: 0px;
		}
	}
	&__info {
		align-items: center;
		display: flex;
		justify-content: center;
	}
	&__overlay {
		stroke: currentColor;
		z-index: 2;
		transition: $loader-overlay-transition;
	}
}

.farm-loader__overlay {
	align-items: center;
	border-radius: inherit;
	background-color: rgba(0, 0, 0, 0.3);
	display: flex;
	justify-content: center;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

@keyframes loader-dash {
	0% {
		stroke-dasharray: 1, 200;
		stroke-dashoffset: 0px;
	}
	50% {
		stroke-dasharray: 100, 200;
		stroke-dashoffset: -15px;
	}
	100% {
		stroke-dasharray: 100, 200;
		stroke-dashoffset: -125px;
	}
}

@keyframes loader-rotate {
	100% {
		transform: rotate(360deg);
	}
}
