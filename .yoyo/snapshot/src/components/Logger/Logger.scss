$colorsList: success, error, info, warning;

.logger {
	display: flex;
	flex-direction: column;

	&--horizontal {
		flex-direction: row;
		gap: 48px;

		.logger__divider {
			width: auto;
			min-width: 192px;
			height: 1px;
			margin: 16px -96px 0 -96px;

			@each $fromColor in $colorsList {
				@each $toColor in $colorsList {
					&.logger__divider--#{$fromColor}-to-#{$toColor} {
						background: linear-gradient(
							to right,
							var(--farm-#{$fromColor}-base),
							var(--farm-#{$toColor}-base)
						);
					}
				}
			}
		}
	}

	&--left-aligned {
		align-items: flex-start;

		.logger__divider {
			margin: 16px -32px 0 -160px;
		}
	}
}

.logger__divider {
	height: 40px;
	margin: -4px 0 -4px 16px;
	width: 1px;

	@each $fromColor in $colorsList {
		@each $toColor in $colorsList {
			&.logger__divider--#{$fromColor}-to-#{$toColor} {
				background: linear-gradient(
					to bottom,
					var(--farm-#{$fromColor}-base),
					var(--farm-#{$toColor}-base)
				);
			}
		}
	}
}
