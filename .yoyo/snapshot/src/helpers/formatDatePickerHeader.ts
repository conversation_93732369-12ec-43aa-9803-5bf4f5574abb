const monthNumberToMonthName = {
	'01': 'Janeiro',
	'02': '<PERSON><PERSON>',
	'03': '<PERSON><PERSON><PERSON>',
	'04': '<PERSON><PERSON>l',
	'05': '<PERSON><PERSON>',
	'06': '<PERSON><PERSON>',
	'07': '<PERSON><PERSON>',
	'08': 'Agosto',
	'09': 'Setembro',
	'10': '<PERSON><PERSON><PERSON>',
	'11': 'Novembro',
	'12': '<PERSON><PERSON><PERSON><PERSON>',
};

export function formatDatePickerHeader(date: string): string {
	const [year, month] = date.split('-');
	return `${month ? monthNumberToMonthName[month] + ' ' : ''}${year}`;
}
