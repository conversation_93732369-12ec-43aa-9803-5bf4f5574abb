@import '../../configurations/variables';

$arrow-size: 6px;
$tooltip-color: #333333;
$arrow-margin: 12px;

.farm-tooltip {
	display: inline-block;
	position: relative;

	&__activator {
		display: inline-block;
	}
}

.farm-tooltip__popup {
	background-color: $tooltip-color;
	visibility: hidden;
	opacity: 0;
	transition: visibility 0.3s linear, opacity 0.3s linear;
	position: absolute;
	width: 160px;
	color: #f5f5f5;
	border-radius: 5px;
	font-family: 'Manrope', sans-serif !important;
	font-size: 12px;
	font-weight: 500px;
	padding: 16px;
	display: block;
	z-index: 9999;

	&--fluid {
		width: auto;
		max-width: 300px;
	}

	&--visible {
		opacity: 1;
		visibility: visible;
	}

	.farm-tooltip__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8px;
	}

	.farm-tooltip__title {
		font-weight: 600;
		font-size: 13px;
		margin-right: 16px;
	}

	.farm-tooltip__content {
		font-weight: 500;
	}

	.farm-tooltip__arrow {
		position: absolute;
		width: 0;
		height: 0;
		border-style: solid;
		z-index: 10000;
	}

	// Top positions - arrow at bottom
	&--top-center .farm-tooltip__arrow,
	&--top-left .farm-tooltip__arrow,
	&--top-right .farm-tooltip__arrow {
		border-width: $arrow-size $arrow-size 0 $arrow-size;
		border-color: $tooltip-color transparent transparent transparent;
		bottom: -$arrow-size;
		z-index: 99999;
	}

	// Bottom positions - arrow at top
	&--bottom-center .farm-tooltip__arrow,
	&--bottom-left .farm-tooltip__arrow,
	&--bottom-right .farm-tooltip__arrow {
		border-width: 0 $arrow-size $arrow-size $arrow-size;
		border-color: transparent transparent $tooltip-color transparent;
		top: -$arrow-size;
		z-index: 99999;
	}

	// Left alignment - arrow at left
	&--top-left .farm-tooltip__arrow,
	&--bottom-left .farm-tooltip__arrow {
		left: $arrow-margin;
	}

	// Right alignment - arrow at right
	&--top-right .farm-tooltip__arrow,
	&--bottom-right .farm-tooltip__arrow {
		right: $arrow-margin;
	}

	// Center alignment - arrow at center
	&--top-center .farm-tooltip__arrow,
	&--bottom-center .farm-tooltip__arrow {
		left: 50%;
		transform: translateX(-50%);
	}

	.farm-tooltip__close {
		position: relative;
		width: 16px;
		height: 16px;
		line-height: 16px;
		text-align: center;
		cursor: pointer;
		font-size: 16px;
		color: #f5f5f5;
	}

	&:not(.farm-tooltip__popup--has-title) .farm-tooltip__close {
		position: absolute;
		top: 8px;
		right: 8px;
	}
}
