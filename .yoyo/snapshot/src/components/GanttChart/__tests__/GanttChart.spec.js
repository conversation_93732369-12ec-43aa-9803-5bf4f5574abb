import { shallowMount } from '@vue/test-utils';
import GanttChart from '../GanttChart.vue';

describe('GanttChart component', () => {
	let wrapper;
	let component;

	const defaultProps = {
		data: {
			groups: [
				{
					title: 'Test Group',
					bars: [
						{
							id: 1,
							label: 'Test Bar',
							start: new Date(2025, 0, 1),
							end: new Date(2025, 1, 1),
							color: '#7BC4F7',
						},
					],
				},
			],
		},
	};

	beforeEach(() => {
		wrapper = shallowMount(GanttChart, {
			propsData: defaultProps,
		});
		component = wrapper.vm;
	});

	test('Created hook', () => {
		expect(wrapper).toBeDefined();
	});

	describe('mount component', () => {
		it('renders correctly', () => {
			expect(wrapper.element).toMatchSnapshot();
		});

		it('renders groups correctly', () => {
			expect(wrapper.findAll('.farm-gantt-chart__group')).toHaveLength(1);
		});

		it('renders month headers', () => {
			const monthHeaders = wrapper.findAll('.farm-gantt-chart__month-header');
			expect(monthHeaders.length).toBeGreaterThan(0);
		});

		it('displays group titles correctly', () => {
			const groupLabel = wrapper.find('.farm-gantt-chart__group-label');
			expect(groupLabel.text()).toContain('Test Group');
		});
	});

	describe('Props', () => {
		it('accepts data prop with new structure', () => {
			expect(component.data).toEqual(defaultProps.data);
		});

		it('validates required data prop structure', () => {
			expect(component.data.groups).toBeDefined();
			expect(Array.isArray(component.data.groups)).toBe(true);
		});

		it('validates group structure', () => {
			const group = component.data.groups[0];
			expect(group.title).toBeDefined();
			expect(group.bars).toBeDefined();
			expect(Array.isArray(group.bars)).toBe(true);
		});

		it('validates bar structure', () => {
			const bar = component.data.groups[0].bars[0];
			expect(bar.id).toBeDefined();
			expect(bar.label).toBeDefined();
			expect(bar.start).toBeDefined();
			expect(bar.end).toBeDefined();
			expect(bar.color).toBeDefined();
		});
	});

	describe('Automatic Calculations', () => {
		describe('Date Range Calculation', () => {
			it('should calculate date range automatically from bars', () => {
				const testData = {
					groups: [
						{
							title: 'Group 1',
							bars: [
								{
									id: 1,
									label: 'Early Bar',
									start: new Date(2025, 0, 15), // Jan 15, 2025
									end: new Date(2025, 2, 10), // Mar 10, 2025
									color: '#7BC4F7',
								},
								{
									id: 2,
									label: 'Late Bar',
									start: new Date(2025, 4, 5), // May 5, 2025
									end: new Date(2025, 6, 20), // Jul 20, 2025
									color: '#8BB455',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				// Should calculate from January 1st to July 31st (full months)
				const monthColumns = testWrapper.vm.monthColumns;
				expect(monthColumns.length).toBe(7); // Jan to Jul = 7 months
				expect(monthColumns[0].label).toContain('Jan');
				expect(monthColumns[monthColumns.length - 1].label).toContain('Jul');
			});

			it('should handle single month range', () => {
				const testData = {
					groups: [
						{
							title: 'Single Month Group',
							bars: [
								{
									id: 1,
									label: 'Single Month Bar',
									start: new Date(2025, 3, 5), // Apr 5, 2025
									end: new Date(2025, 3, 25), // Apr 25, 2025
									color: '#7BC4F7',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				const monthColumns = testWrapper.vm.monthColumns;
				expect(monthColumns.length).toBe(1);
				expect(monthColumns[0].label).toContain('Abr');
			});
		});

		describe('Legend Generation', () => {
			it('should generate legend automatically from unique colors and labels', () => {
				const testData = {
					groups: [
						{
							title: 'Group 1',
							bars: [
								{
									id: 1,
									label: 'Design',
									start: new Date(2025, 0, 1),
									end: new Date(2025, 1, 1),
									color: '#8E44AD',
								},
								{
									id: 2,
									label: 'Development',
									start: new Date(2025, 1, 1),
									end: new Date(2025, 2, 1),
									color: '#16A085',
								},
								{
									id: 3,
									label: 'Design', // Same label, same color - should not duplicate
									start: new Date(2025, 2, 1),
									end: new Date(2025, 3, 1),
									color: '#8E44AD',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				const legend = testWrapper.vm.autoGeneratedLegend;
				expect(legend).toHaveLength(2); // Only unique combinations
				expect(legend.some(item => item.label === 'Design' && item.color === '#8E44AD')).toBe(true);
				expect(legend.some(item => item.label === 'Development' && item.color === '#16A085')).toBe(true);
			});

			it('should display generated legend in template', () => {
				const legendItems = wrapper.findAll('.farm-gantt-chart__legend-item');
				expect(legendItems.length).toBeGreaterThan(0);
			});
		});
	});

	describe('Methods', () => {
		describe('getPositionedBars', () => {
			it('should position bars correctly with each bar in its own row', () => {
				const bars = [
					{
						id: 1,
						start: new Date(2025, 0, 1),
						end: new Date(2025, 1, 1),
						label: 'Bar 1',
						color: '#7BC4F7',
					},
					{
						id: 2,
						start: new Date(2025, 0, 15),
						end: new Date(2025, 1, 15),
						label: 'Bar 2',
						color: '#8BB455',
					},
				];
				const positionedBars = component.getPositionedBars(bars);
				expect(positionedBars).toHaveLength(2);
				// Each bar should have its own unique row position
				expect(positionedBars[0].rowPosition).toBe(0);
				expect(positionedBars[1].rowPosition).toBe(1);
			});

			it('should position non-overlapping bars in separate rows', () => {
				const bars = [
					{
						id: 1,
						start: new Date(2025, 0, 1),
						end: new Date(2025, 0, 15),
						label: 'Early Bar',
						color: '#7BC4F7',
					},
					{
						id: 2,
						start: new Date(2025, 1, 1), // No overlap with first bar
						end: new Date(2025, 1, 15),
						label: 'Later Bar',
						color: '#8BB455',
					},
					{
						id: 3,
						start: new Date(2025, 2, 1), // No overlap with other bars
						end: new Date(2025, 2, 15),
						label: 'Latest Bar',
						color: '#FFB84D',
					},
				];
				const positionedBars = component.getPositionedBars(bars);
				expect(positionedBars).toHaveLength(3);
				// Even non-overlapping bars should be in separate rows
				expect(positionedBars[0].rowPosition).toBe(0);
				expect(positionedBars[1].rowPosition).toBe(1);
				expect(positionedBars[2].rowPosition).toBe(2);
			});

			it('should handle empty bars array', () => {
				const positionedBars = component.getPositionedBars([]);
				expect(positionedBars).toEqual([]);
			});

			it('should handle invalid dates gracefully', () => {
				const bars = [
					{
						id: 1,
						start: 'invalid-date',
						end: 'invalid-date',
						label: 'Invalid Bar',
						color: '#7BC4F7',
					},
				];
				const positionedBars = component.getPositionedBars(bars);
				expect(positionedBars).toHaveLength(1);
				expect(positionedBars[0].rowPosition).toBeDefined();
			});
		});

		describe('getBarGridStyle', () => {
			it('should return correct grid style for bars', () => {
				const bar = {
					id: 1,
					start: new Date(2025, 0, 1),
					end: new Date(2025, 1, 1),
					label: 'Test Bar',
					color: '#7BC4F7',
					rowPosition: 0,
				};
				const style = component.getBarGridStyle(bar);
				expect(style['background-color']).toBe('#7BC4F7');
				expect(style['grid-row']).toBe('1');
				expect(style['grid-column-start']).toBeDefined();
				expect(style['grid-column-end']).toBeDefined();
			});

		});
	});

	describe('Color Fallback', () => {
		it('should use fallback color when bar.color is not provided', () => {
			const testData = {
				groups: [{
					title: 'Test Group',
					bars: [{
						id: 1,
						label: 'Bar Without Color',
						start: new Date(2025, 0, 1),
						end: new Date(2025, 1, 1),
						// color não definido - deve usar fallback
					}]
				}]
			};

			const testWrapper = shallowMount(GanttChart, {
				propsData: { data: testData },
			});

			const bar = {
				id: 1,
				label: 'Bar Without Color',
				start: new Date(2025, 0, 1),
				end: new Date(2025, 1, 1),
				rowPosition: 0
			};
			
			const style = testWrapper.vm.getBarGridStyle(bar);
			expect(style['background-color']).toBe('var(--farm-primary-base)');
		});

		it('should use provided color when bar.color is defined', () => {
			const bar = {
				id: 1,
				label: 'Bar With Color',
				start: new Date(2025, 0, 1),
				end: new Date(2025, 1, 1),
				color: '#FF5733',
				rowPosition: 0
			};
			
			const style = component.getBarGridStyle(bar);
			expect(style['background-color']).toBe('#FF5733');
		});
	});

	describe('Data Validation', () => {
		it('should validate invalid data prop structure', () => {
			const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
			
			// Teste com data null
			const validator = wrapper.vm.$options.props.data.validator;
			expect(validator(null)).toBe(false);
			expect(consoleSpy).toHaveBeenCalledWith('GanttChart: prop "data" deve ser um objeto.');
			
			// Teste com data sem groups
			expect(validator({})).toBe(false);
			expect(consoleSpy).toHaveBeenCalledWith('GanttChart: prop "data.groups" deve ser um array.');
			
			// Teste com groups inválido
			expect(validator({ groups: 'invalid' })).toBe(false);
			expect(consoleSpy).toHaveBeenCalledWith('GanttChart: prop "data.groups" deve ser um array.');
			
			consoleSpy.mockRestore();
		});

		it('should validate group structure', () => {
			const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
			const validator = wrapper.vm.$options.props.data.validator;
			
			// Teste com grupo sem title
			const invalidData = {
				groups: [{
					bars: []
				}]
			};
			expect(validator(invalidData)).toBe(false);
			expect(consoleSpy).toHaveBeenCalledWith('GanttChart: cada grupo deve ter título (string) e barras (array).');
			
			// Teste com grupo sem bars
			const invalidData2 = {
				groups: [{
					title: 'Valid Title'
				}]
			};
			expect(validator(invalidData2)).toBe(false);
			expect(consoleSpy).toHaveBeenCalledWith('GanttChart: cada grupo deve ter título (string) e barras (array).');
			
			consoleSpy.mockRestore();
		});

		it('should validate correct data structure', () => {
			const validator = wrapper.vm.$options.props.data.validator;
			const validData = {
				groups: [{
					title: 'Valid Group',
					bars: []
				}]
			};
			expect(validator(validData)).toBe(true);
		});
	});

	describe('Date Handling', () => {
		it('should correct dates when end is before start', () => {
			const bar = {
				id: 1,
				label: 'Inverted Date Bar',
				// Datas invertidas (end antes de start)
				start: new Date(2025, 3, 15), // 15 de abril
				end: new Date(2025, 2, 15),   // 15 de março (antes do início)
				color: '#7BC4F7',
			};
			
			// Normalizar barra via função de posicionamento
			const dates = component.normalizeBarDates(bar);
			
			// Verificar se as datas foram invertidas corretamente
			expect(dates.startDate.getTime()).toBe(new Date(2025, 2, 15).getTime()); // Agora é 15 de março
			expect(dates.endDate.getTime()).toBe(new Date(2025, 3, 15).getTime()); // Agora é 15 de abril
		});

		it('should handle invalid dates', () => {
			const bar = {
				id: 1,
				label: 'Invalid Date Bar',
				start: 'invalid-date',
				end: 'invalid-date',
				color: '#7BC4F7',
			};
			
			const dates = component.normalizeBarDates(bar);
			expect(dates).toBeNull();
		});
	});

	describe('Tooltip System', () => {
		it('should show tooltip on bar mouseenter', async () => {
			// Encontrar uma barra
			const bar = wrapper.find('.farm-gantt-chart__bar');
			
			if (bar.exists()) {
				// Disparar evento mouseenter
				await bar.trigger('mouseenter', {
					clientX: 100,
					clientY: 100,
				});
				
				// Verificar se tooltipState foi atualizado
				expect(component.tooltipState.visible).toBe(true);
				expect(component.tooltipState.title).toBe('Test Bar');
				expect(component.tooltipState.barData).not.toBeNull();
			}
		});
		
		it('should hide tooltip on bar mouseleave', async () => {
			// Preparar estado (mostrar tooltip)
			component.tooltipState.visible = true;
			component.tooltipState.title = 'Teste';
			component.tooltipState.barData = { label: 'Teste' };
			
			// Encontrar e disparar mouseleave
			const bar = wrapper.find('.farm-gantt-chart__bar');
			if (bar.exists()) {
				await bar.trigger('mouseleave');
				
				// Verificar se tooltipState foi atualizado
				expect(component.tooltipState.visible).toBe(false);
				expect(component.tooltipState.barData).toBeNull();
			}
		});
		
		it('should show structured tooltip when tooltipData is available', () => {
			// Criar wrapper com dados para tooltip
			const testData = {
				groups: [{
					title: 'Group with Tooltips',
					bars: [{
						id: 1,
						label: 'Bar with Tooltip',
						start: new Date(2025, 0, 1),
						end: new Date(2025, 1, 1),
						color: '#7BC4F7',
						tooltipData: {
							'Taxa': '1,75%',
							'Vigência': '01/01/2025 a 31/01/2025'
						}
					}]
				}]
			};
			
			const tooltipWrapper = shallowMount(GanttChart, {
				propsData: { data: testData },
			});
			
			// Simular mouseenter
			const barWithTooltip = tooltipWrapper.find('.farm-gantt-chart__bar');
			if (barWithTooltip.exists()) {
				barWithTooltip.trigger('mouseenter', {
					clientX: 100,
					clientY: 100
				});
				
				// Verificar que tooltip com dados estruturados será exibido
				// quando tooltipState.barData.tooltipData existir
				expect(tooltipWrapper.vm.tooltipState.barData.tooltipData).toBeDefined();
				expect(tooltipWrapper.vm.tooltipState.barData.tooltipData.Taxa).toBe('1,75%');
			}
		});
	});

	describe('Empty Data Handling', () => {
		it('should handle empty groups array gracefully', () => {
			const emptyData = { groups: [] };
			const emptyWrapper = shallowMount(GanttChart, {
				propsData: { data: emptyData },
			});
			
			// Componente deve renderizar sem erros mesmo com dados vazios
			expect(emptyWrapper.exists()).toBe(true);
			expect(emptyWrapper.find('.farm-gantt-chart').exists()).toBe(true);
			
			// Não deve haver grupos renderizados
			const groups = emptyWrapper.findAll('.farm-gantt-chart__group');
			expect(groups.length).toBe(0);
		});
		
		it('should render timeline headers even with empty data', () => {
			const emptyData = { groups: [] };
			const emptyWrapper = shallowMount(GanttChart, {
				propsData: { data: emptyData },
			});
			
			// Timeline headers devem estar presentes (baseado em data range padrão ou calculado)
			const header = emptyWrapper.find('.farm-gantt-chart__header');
			expect(header.exists()).toBe(true);
		});
	});

	describe('Computed Properties', () => {
		it('should generate month columns correctly', () => {
			expect(component.monthColumns).toBeDefined();
			expect(Array.isArray(component.monthColumns)).toBe(true);
			expect(component.monthColumns.length).toBeGreaterThan(0);
		});

		it('should generate timeline grid style', () => {
			expect(component.timelineGridStyle).toBeDefined();
			expect(component.timelineGridStyle.gridTemplateColumns).toBeDefined();
		});

		it('should calculate component style with height', () => {
			expect(component.componentStyle).toBeDefined();
			expect(component.componentStyle['--gantt-content-height']).toBeDefined();
		});
	});


});
