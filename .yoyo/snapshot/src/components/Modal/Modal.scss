@import '../../configurations/mixins';

.farm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    align-items: center;
    display: flex;
    justify-content: center;
    transition: .2s cubic-bezier(0.25, 0.8, 0.25, 1), z-index 1ms;

    &--overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        opacity: 0.36;
        background-color: var(--farm-gray-darken);
        z-index: 101;
    }

    &--container {
        background-color: white;
        border-radius: 4px;
        max-height: calc(100vh - 48px);
        position: relative;
        overflow: hidden;
        z-index: 102;
        @include addShadow;
    }

    &--content {
        max-height: calc(100vh - 48px);
        width: 100%;
        padding: 0;
        >div {
            overflow-y: auto;
            padding: 0 16px;
        }
    }

    &--header {
        position: absolute;
        top: 0;
        background: white;
        width: 100%;
    }

    &--footer {
        position: absolute;
        bottom: 0;
        background: white;
        width: 100%;
    }

}

.fade-enter-active,
.fade-leave-active {
    transition: opacity .25s
}

.fade-enter,
.fade-leave-to {
    opacity: 0
}

@include upToSm {
    .farm-modal {
        &--container {
            width: calc(100vw - 32px);
            max-width: calc(100vw - 32px);
        }
    }
}

@include fromSm {
    .farm-modal {
        &--size-default {
            .farm-modal--container {
                width: 960px;
                max-width: 960px;
            }
        }

        &--size-md {
            .farm-modal--container {
                width: 568px;
                max-width: 568px;
            }
        }

        &--size-sm {
            .farm-modal--container {
                width: 448px;
                max-width: 448px;
            }
        }

        &--size-xs {
            .farm-modal--container {
                width: 360px;
                max-width: 360px;
            }
        }
    }
}