@mixin stepperHeaderStepColor($color) {
	.farm-icon,
	.farm-icon__number {
		background-color: $color;
		border-color: $color;
	}

	.farm-icon:before,
	.farm-icon__number {
		color: white;
	}

	.farm-bodytext--2 {
		color: $color;
	}

	.farm-bodytext--2.farm-icon__number {
		color: white;
	}
}

@mixin stepperDividerGradient($direction) {
	&.stepper__divider--previous-to-current {
		background: linear-gradient(to #{$direction}, var(--farm-primary-base), #5089de);
	}

	&.stepper__divider--previous-to-error {
		background: linear-gradient(
			to #{$direction},
			var(--farm-primary-base),
			var(--farm-error-base)
		);
	}

	&.stepper__divider--error-to-next {
		background: linear-gradient(
			to #{$direction},
			var(--farm-error-base),
			var(--farm-gray-lighten)
		);
	}

	&.stepper__divider--current-to-next {
		background: linear-gradient(to #{$direction}, #5089de, var(--farm-gray-lighten));
	}
}
