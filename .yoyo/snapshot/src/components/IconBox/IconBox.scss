@import '../../configurations/variables';
@import '../../configurations/theme-colors';

.farm-icon-box {
	width: 48px;
	height: 48px;
	border-radius: 8px;
	display: flex;
	justify-content: center;
	align-items: center;

	@each $color in $theme-colors-list {
		&#{'--' + $color} {
			background: var(--farm-#{$color}-lighten);
		}
	}

	&--neutral .farm-icon.farm-icon {
		color: var(--farm-neutral-darken);
	}
	&--secondary .farm-icon.farm-icon,
	&--gray .farm-icon.farm-icon {
		color: white;
	}
}

.farm-icon-box--inverted.farm-icon-box {
	@each $color in $theme-colors-list {
		&#{'--' + $color} {
			background: var(--farm-#{$color}-base);
		}
	}

	.farm-icon.farm-icon--white {
		color: white;
	}

	&--neutral .farm-icon.farm-icon--white {
		color: var(--farm-neutral-darken);
	}

}